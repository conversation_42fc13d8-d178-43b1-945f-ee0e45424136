{"mcpServers": {"csharp-assistant": {"command": "python", "args": ["src/csharp_assistant/mcp_server.py"], "cwd": "d:/Project/Super-Project-Assistant", "env": {"PROJECT_NAME": "Game-6", "PROJECT_PATH": "E:/Github/4.2.1/Game-6", "LANGUAGE_SERVER_ENABLED": "true", "LANGUAGE_SERVER_PATH": "D:/omnisharp/OmniSharp.exe", "LLM_PROVIDER": "ollama", "AI_PROVIDER": "ollama", "OLLAMA_BASE_URL": "http://localhost:11434", "OLLAMA_MODEL": "qwen2.5:3b", "ARANGODB_PASSWORD": "12345678"}}}}