#!/usr/bin/env python3
"""
C# Assistant CLI

命令行界面，用于启动和管理C# Assistant MCP服务器。
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.csharp_assistant.mcp_server import MCPServer
from src.csharp_assistant.config.config import get_config

app = typer.Typer(
    name="csharp-assistant",
    help="C# Assistant - 智能C#代码分析和建议工具",
    add_completion=False
)
console = Console()


@app.command("start")
def start_server(
    project_path: Optional[str] = typer.Option(
        None,
        "--project-path", "-p",
        help="C#项目路径"
    ),
    project_name: Optional[str] = typer.Option(
        None,
        "--project-name", "-n", 
        help="项目名称"
    ),
    ai_provider: Optional[str] = typer.Option(
        None,
        "--ai-provider", "-a",
        help="AI服务提供商 (openai/gemini/ollama)"
    ),
    verbose: bool = typer.Option(
        False,
        "--verbose", "-v",
        help="启用详细日志输出"
    )
):
    """启动C# Assistant MCP服务器"""
    
    # 显示启动信息
    console.print(Panel.fit(
        Text("C# Assistant MCP Server", style="bold blue"),
        title="启动中...",
        border_style="blue"
    ))
    
    # 设置环境变量（如果提供了参数）
    import os
    if project_path:
        os.environ["PROJECT_PATH"] = project_path
    if project_name:
        os.environ["PROJECT_NAME"] = project_name
    if ai_provider:
        os.environ["AI_PROVIDER"] = ai_provider
    if verbose:
        os.environ["LOG_LEVEL"] = "DEBUG"
    
    # 显示配置信息
    config = get_config()
    console.print(f"项目路径: {config.project_path}")
    console.print(f"项目名称: {config.project_name}")
    console.print(f"AI提供商: {config.ai_provider}")
    console.print(f"语言服务器: {'启用' if config.language_server_enabled else '禁用'}")
    console.print()
    
    # 启动服务器
    async def run_server():
        server = MCPServer()
        await server.start()
    
    try:
        asyncio.run(run_server())
    except KeyboardInterrupt:
        console.print("\n[yellow]服务器已停止[/yellow]")
    except Exception as e:
        console.print(f"[red]启动失败: {e}[/red]")
        raise typer.Exit(1)


@app.command("config")
def show_config():
    """显示当前配置"""
    config = get_config()
    
    console.print(Panel.fit(
        Text("当前配置", style="bold green"),
        border_style="green"
    ))
    
    config_info = f"""
项目路径: {config.project_path}
项目名称: {config.project_name}
AI提供商: {config.ai_provider}
语言服务器路径: {config.language_server_path}
语言服务器启用: {'是' if config.language_server_enabled else '否'}
MCP主机: {config.mcp_host}
MCP端口: {config.mcp_port}
数据库URL: {config.database_url}
"""
    
    console.print(config_info.strip())


@app.command("test")
def test_connection(
    component: str = typer.Argument(
        "all",
        help="要测试的组件 (all/database/ai/omnisharp)"
    )
):
    """测试各组件连接"""

    async def run_tests():
        from src.csharp_assistant.core.application import Application

        console.print(Panel.fit(
            Text("连接测试", style="bold yellow"),
            border_style="yellow"
        ))

        app = Application()
        test_results = {"passed": 0, "failed": 0, "skipped": 0}

        if component in ["all", "database"]:
            console.print("🔍 测试数据库连接...")
            try:
                await app.knowledge_graph_manager.initialize_schema()
                console.print("[green]✓ 数据库连接正常[/green]")
                test_results["passed"] += 1
            except Exception as e:
                console.print(f"[red]✗ 数据库连接失败: {e}[/red]")
                test_results["failed"] += 1

        if component in ["all", "ai"]:
            console.print("🤖 测试AI服务连接...")
            try:
                ai_service = app.ai_manager.get_service()
                if ai_service:
                    # 简单测试
                    result = await ai_service.get_suggestions({}, "测试连接")
                    console.print("[green]✓ AI服务连接正常[/green]")
                    test_results["passed"] += 1
                else:
                    console.print("[yellow]! AI服务未配置[/yellow]")
                    test_results["skipped"] += 1
            except Exception as e:
                console.print(f"[red]✗ AI服务连接失败: {e}[/red]")
                test_results["failed"] += 1

        if component in ["all", "omnisharp"]:
            console.print("🔧 测试OmniSharp连接...")
            try:
                config = get_config()
                if config.language_server_enabled and config.language_server_path:
                    await app.omnisharp_client.start(config.project_path)
                    console.print("[green]✓ OmniSharp连接正常[/green]")
                    await app.omnisharp_client.stop()
                    test_results["passed"] += 1
                else:
                    console.print("[yellow]! OmniSharp未配置或已禁用[/yellow]")
                    test_results["skipped"] += 1
            except Exception as e:
                console.print(f"[red]✗ OmniSharp连接失败: {e}[/red]")
                test_results["failed"] += 1

        # 显示测试总结
        console.print()
        console.print(Panel.fit(
            f"测试完成: [green]{test_results['passed']} 通过[/green], "
            f"[red]{test_results['failed']} 失败[/red], "
            f"[yellow]{test_results['skipped']} 跳过[/yellow]",
            title="测试结果",
            border_style="blue"
        ))

        return test_results["failed"] == 0

    try:
        success = asyncio.run(run_tests())
        if not success:
            raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]测试失败: {e}[/red]")
        raise typer.Exit(1)


@app.command("analyze")
def analyze_file(
    file_path: str = typer.Argument(help="要分析的C#文件路径"),
    output_format: str = typer.Option("table", "--format", "-f", help="输出格式 (table/json)")
):
    """分析指定的C#文件"""

    async def run_analysis():
        from src.csharp_assistant.core.application import Application
        import json
        from rich.table import Table

        if not os.path.exists(file_path):
            console.print(f"[red]文件不存在: {file_path}[/red]")
            return False

        if not file_path.endswith('.cs'):
            console.print(f"[red]只支持C#文件(.cs): {file_path}[/red]")
            return False

        console.print(f"🔍 分析文件: {file_path}")

        app = Application()

        try:
            # 启动OmniSharp（如果需要）
            config = get_config()
            if config.language_server_enabled:
                await app.omnisharp_client.start(config.project_path)

            # 分析文件
            result = await app.omnisharp_client.analyze_code(file_path)

            if not result:
                console.print("[red]文件分析失败[/red]")
                return False

            if output_format == "json":
                console.print(json.dumps(result, indent=2, ensure_ascii=False))
            else:
                # 表格格式输出
                if result.get("classes"):
                    table = Table(title="类信息")
                    table.add_column("类名", style="cyan")
                    table.add_column("命名空间", style="green")
                    table.add_column("访问修饰符", style="yellow")
                    table.add_column("特性", style="magenta")

                    for cls in result["classes"]:
                        attributes = []
                        if cls.get("is_static"): attributes.append("static")
                        if cls.get("is_abstract"): attributes.append("abstract")
                        if cls.get("is_sealed"): attributes.append("sealed")

                        table.add_row(
                            cls.get("name", ""),
                            cls.get("namespace", ""),
                            cls.get("access_modifier", ""),
                            ", ".join(attributes)
                        )
                    console.print(table)

                if result.get("methods"):
                    table = Table(title="方法信息")
                    table.add_column("方法名", style="cyan")
                    table.add_column("返回类型", style="green")
                    table.add_column("访问修饰符", style="yellow")
                    table.add_column("特性", style="magenta")

                    for method in result["methods"]:
                        attributes = []
                        if method.get("is_static"): attributes.append("static")
                        if method.get("is_async"): attributes.append("async")
                        if method.get("is_virtual"): attributes.append("virtual")
                        if method.get("is_override"): attributes.append("override")

                        table.add_row(
                            method.get("name", ""),
                            method.get("return_type", ""),
                            method.get("access_modifier", ""),
                            ", ".join(attributes)
                        )
                    console.print(table)

            # 停止OmniSharp
            if config.language_server_enabled:
                await app.omnisharp_client.stop()

            return True

        except Exception as e:
            console.print(f"[red]分析失败: {e}[/red]")
            return False

    try:
        success = asyncio.run(run_analysis())
        if not success:
            raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]分析失败: {e}[/red]")
        raise typer.Exit(1)

@app.command("query")
def query_project(
    question: str = typer.Argument(help="要查询的问题"),
    project_name: str = typer.Option(None, "--project", "-p", help="项目名称")
):
    """查询项目知识图谱"""

    async def run_query():
        from src.csharp_assistant.core.application import Application

        config = get_config()
        if not project_name:
            project_name_to_use = config.project_name
        else:
            project_name_to_use = project_name

        console.print(f"🔍 查询项目: {project_name_to_use}")
        console.print(f"❓ 问题: {question}")

        app = Application()

        try:
            # 初始化知识图谱
            await app.knowledge_graph_manager.initialize_schema()

            # 查询上下文
            context = await app.knowledge_graph_manager.query_context(question)

            if not context:
                console.print("[yellow]未找到相关信息[/yellow]")
                return True

            console.print(f"\n📊 找到 {len(context)} 个相关结果:")

            from rich.table import Table
            table = Table()
            table.add_column("类型", style="cyan")
            table.add_column("名称", style="green")
            table.add_column("详情", style="yellow")

            for item in context[:10]:  # 限制显示数量
                item_type = item.get("type", "unknown")
                name = item.get("name", item.get("full_name", ""))
                detail = item.get("namespace", item.get("return_type", ""))

                table.add_row(item_type, name, detail)

            console.print(table)

            # 如果有AI服务，获取AI分析
            ai_service = app.ai_manager.get_service()
            if ai_service:
                console.print("\n🤖 AI分析:")
                try:
                    suggestions = await ai_service.get_suggestions(
                        context={"knowledge_graph_context": context},
                        query=question
                    )
                    for suggestion in suggestions[:3]:  # 显示前3个建议
                        console.print(f"• {suggestion}")
                except Exception as e:
                    console.print(f"[yellow]AI分析失败: {e}[/yellow]")

            return True

        except Exception as e:
            console.print(f"[red]查询失败: {e}[/red]")
            return False

    try:
        success = asyncio.run(run_query())
        if not success:
            raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]查询失败: {e}[/red]")
        raise typer.Exit(1)

@app.command("version")
def show_version():
    """显示版本信息"""
    console.print(Panel.fit(
        Text("C# Assistant v1.0.0", style="bold cyan"),
        subtitle="智能C#代码分析和建议工具",
        border_style="cyan"
    ))


def main():
    """CLI主入口点"""
    app()


if __name__ == "__main__":
    main()
