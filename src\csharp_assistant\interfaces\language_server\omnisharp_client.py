import asyncio
import json
import logging
import os
from typing import Any, Dict, List, Optional
from pathlib import Path
from urllib.parse import urljoin

from ...application.ports.language_server_port import LanguageServerPort
from ...config.config import get_config

logger = logging.getLogger(__name__)


class OmniSharpClient(LanguageServerPort):
    """OmniSharp 语言服务器客户端"""

    def __init__(self):
        self.config = get_config()
        self.process = None
        self.reader = None
        self.writer = None
        self.request_id = 0
        self.project_path = None
        self.is_initialized = False

    def _get_next_request_id(self) -> int:
        """获取下一个请求ID"""
        self.request_id += 1
        return self.request_id

    def _path_to_uri(self, file_path: str) -> str:
        """将文件路径转换为URI格式"""
        path = Path(file_path).resolve()
        if os.name == 'nt':  # Windows
            return f"file:///{str(path).replace(os.sep, '/')}"
        else:
            return f"file://{str(path)}"

    async def start(self, project_path: str):
        """启动 OmniSharp 语言服务器"""
        if not self.config.language_server_enabled:
            logger.info("语言服务器已禁用，跳过启动")
            return

        if not self.config.language_server_path:
            logger.error("未配置 LANGUAGE_SERVER_PATH，无法启动 OmniSharp")
            return

        self.project_path = str(Path(project_path).resolve())
        logger.info(f"正在启动 OmniSharp 语言服务器: {self.config.language_server_path}")

        try:
            # 检查项目类型
            project_files = list(Path(self.project_path).glob("*.csproj"))
            sln_files = list(Path(self.project_path).glob("*.sln"))

            # 确定要传递给OmniSharp的项目文件
            project_file = None
            if sln_files:
                project_file = str(sln_files[0])
                logger.info(f"找到解决方案文件: {project_file}")
            elif project_files:
                project_file = str(project_files[0])
                logger.info(f"找到项目文件: {project_file}")
            else:
                logger.warning("未找到.sln或.csproj文件，使用目录路径")
                project_file = self.project_path

            # 启动OmniSharp进程
            cmd_args = [
                self.config.language_server_path,
                "-lsp",  # 使用LSP模式
                "-s", project_file,
                "--hostPID", str(os.getpid()),
                "--encoding", "utf-8"
            ]

            logger.info(f"启动OmniSharp命令: {' '.join(cmd_args)}")

            self.process = await asyncio.create_subprocess_exec(
                *cmd_args,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                stdin=asyncio.subprocess.PIPE,
            )

            # 等待进程启动
            await asyncio.sleep(2.0)  # 增加等待时间
            if self.process.returncode is not None:
                stderr_output = await self.process.stderr.read()
                logger.error(f"OmniSharp process exited immediately with code: {self.process.returncode}")
                logger.error(f"Stderr: {stderr_output.decode('utf-8', errors='ignore')}")
                return

            self.reader = self.process.stdout
            self.writer = self.process.stdin
            logger.info("OmniSharp 语言服务器已启动")

            # 发送初始化请求
            await self._initialize()

        except FileNotFoundError:
            logger.error(f"找不到 OmniSharp 可执行文件: {self.config.language_server_path}")
        except Exception as e:
            logger.error(f"启动 OmniSharp 语言服务器失败: {e}", exc_info=True)

    async def _initialize(self):
        """初始化LSP连接"""
        try:
            logger.info(f"开始初始化OmniSharp LSP连接，项目路径: {self.project_path}")

            # 检查进程状态
            if self.process.returncode is not None:
                logger.error(f"OmniSharp进程已退出，返回码: {self.process.returncode}")
                return

            # 发送initialize请求
            initialize_request = {
                "jsonrpc": "2.0",
                "id": self._get_next_request_id(),
                "method": "initialize",
                "params": {
                    "processId": os.getpid(),
                    "rootUri": self._path_to_uri(self.project_path),
                    "initializationOptions": {
                        "enableRoslynAnalyzers": True,
                        "enableEditorConfigSupport": True,
                        "enableMsBuildLoadProjectsOnDemand": False,
                        "enableImportCompletion": True,
                        "sdkIncludePrereleases": True
                    },
                    "capabilities": {
                        "textDocument": {
                            "synchronization": {
                                "didOpen": True,
                                "didChange": True,
                                "didClose": True,
                                "willSave": True,
                                "willSaveWaitUntil": True
                            },
                            "documentSymbol": {
                                "symbolKind": {
                                    "valueSet": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]
                                },
                                "hierarchicalDocumentSymbolSupport": True
                            },
                            "publishDiagnostics": {
                                "relatedInformation": True,
                                "versionSupport": True,
                                "tagSupport": {
                                    "valueSet": [1, 2]
                                }
                            },
                            "completion": {
                                "completionItem": {
                                    "snippetSupport": True,
                                    "commitCharactersSupport": True,
                                    "documentationFormat": ["markdown", "plaintext"]
                                }
                            }
                        },
                        "workspace": {
                            "workspaceFolders": True,
                            "configuration": True,
                            "didChangeConfiguration": {
                                "dynamicRegistration": True
                            }
                        }
                    },
                    "workspaceFolders": [
                        {
                            "uri": self._path_to_uri(self.project_path),
                            "name": Path(self.project_path).name
                        }
                    ]
                },
            }

            logger.info("发送initialize请求到OmniSharp")
            response = await self._send_request(initialize_request)

            if response and "result" in response:
                logger.info("OmniSharp 初始化成功")
                logger.debug(f"初始化响应: {response}")
                self.is_initialized = True

                # 发送initialized通知
                initialized_notification = {
                    "jsonrpc": "2.0",
                    "method": "initialized",
                    "params": {},
                }
                await self._send_notification(initialized_notification)
                logger.info("已发送initialized通知")
            else:
                logger.error(f"OmniSharp 初始化失败，响应: {response}")
                # 尝试读取stderr获取更多错误信息
                if self.process and self.process.stderr:
                    try:
                        stderr_data = await asyncio.wait_for(self.process.stderr.read(1024), timeout=1.0)
                        if stderr_data:
                            logger.error(f"OmniSharp stderr: {stderr_data.decode('utf-8', errors='ignore')}")
                    except asyncio.TimeoutError:
                        pass

        except Exception as e:
            logger.error(f"初始化 OmniSharp 失败: {e}", exc_info=True)

    async def _send_request(self, request: Dict[str, Any], timeout: float = 30.0) -> Optional[Dict[str, Any]]:
        """发送 LSP 请求并等待响应"""
        if not self.writer or not self.reader:
            logger.error("LSP连接未建立")
            return None

        try:
            message = json.dumps(request, ensure_ascii=False).encode("utf-8")
            content_length = len(message)
            headers = f"Content-Length: {content_length}\r\n\r\n".encode("utf-8")

            # 发送请求
            self.writer.write(headers + message)
            await self.writer.drain()

            logger.debug(f"发送LSP请求: {request['method']}")

            # 等待响应
            response = await asyncio.wait_for(self._read_message(), timeout=timeout)

            if response and "error" in response:
                logger.error(f"LSP请求错误: {response['error']}")
                return None

            return response

        except asyncio.TimeoutError:
            logger.error(f"LSP请求超时: {request['method']}")
            return None
        except Exception as e:
            logger.error(f"发送LSP请求失败: {e}", exc_info=True)
            return None

    async def _send_notification(self, notification: Dict[str, Any]) -> None:
        """发送 LSP 通知"""
        if not self.writer:
            logger.error("LSP连接未建立")
            return

        try:
            message = json.dumps(notification, ensure_ascii=False).encode("utf-8")
            content_length = len(message)
            headers = f"Content-Length: {content_length}\r\n\r\n".encode("utf-8")

            self.writer.write(headers + message)
            await self.writer.drain()

            logger.debug(f"发送LSP通知: {notification['method']}")

        except Exception as e:
            logger.error(f"发送LSP通知失败: {e}", exc_info=True)

    async def _read_message(self) -> Optional[Dict[str, Any]]:
        """读取LSP消息"""
        try:
            # 读取头部
            headers = {}
            while True:
                line = await self.reader.readline()
                if not line:
                    return None

                line = line.decode("utf-8").strip()
                if not line:
                    break

                if ": " in line:
                    key, value = line.split(": ", 1)
                    headers[key.lower()] = value

            # 获取内容长度
            content_length = int(headers.get("content-length", 0))
            if content_length == 0:
                return None

            # 读取消息体
            message_body = await self.reader.readexactly(content_length)
            return json.loads(message_body.decode("utf-8"))

        except Exception as e:
            logger.error(f"读取LSP消息失败: {e}", exc_info=True)
            return None

    async def stop(self):
        """停止 OmniSharp 语言服务器"""
        if self.process and self.process.returncode is None:
            logger.info("正在停止 OmniSharp 语言服务器")

            try:
                # 发送shutdown请求
                shutdown_request = {
                    "jsonrpc": "2.0",
                    "id": self._get_next_request_id(),
                    "method": "shutdown",
                    "params": None
                }
                await self._send_request(shutdown_request, timeout=5.0)

                # 发送exit通知
                exit_notification = {
                    "jsonrpc": "2.0",
                    "method": "exit",
                    "params": None
                }
                await self._send_notification(exit_notification)

                # 等待进程结束
                await asyncio.wait_for(self.process.wait(), timeout=5.0)

            except asyncio.TimeoutError:
                logger.warning("OmniSharp进程未在超时时间内结束，强制终止")
                self.process.terminate()
                await self.process.wait()
            except Exception as e:
                logger.error(f"停止OmniSharp时出错: {e}")
                self.process.terminate()
                await self.process.wait()

            self.is_initialized = False
            logger.info("OmniSharp 语言服务器已停止")

    async def analyze_code(self, file_path: str) -> Dict[str, Any]:
        """分析代码文件"""
        if not self.is_initialized:
            logger.warning("OmniSharp未初始化，返回空结果")
            return {"classes": [], "methods": [], "fields": [], "diagnostics": []}

        logger.info(f"分析代码文件: {file_path}")

        try:
            file_uri = self._path_to_uri(file_path)

            # 检查文件是否存在
            if not Path(file_path).exists():
                logger.error(f"文件不存在: {file_path}")
                return {"classes": [], "methods": [], "fields": [], "diagnostics": []}

            # 读取文件内容
            try:
                file_content = Path(file_path).read_text(encoding='utf-8')
            except UnicodeDecodeError:
                try:
                    file_content = Path(file_path).read_text(encoding='gbk')
                except UnicodeDecodeError:
                    logger.error(f"无法读取文件编码: {file_path}")
                    return {"classes": [], "methods": [], "fields": [], "diagnostics": []}

            # 发送textDocument/didOpen通知
            did_open_notification = {
                "jsonrpc": "2.0",
                "method": "textDocument/didOpen",
                "params": {
                    "textDocument": {
                        "uri": file_uri,
                        "languageId": "csharp",
                        "version": 1,
                        "text": file_content,
                    }
                },
            }
            await self._send_notification(did_open_notification)

            # 等待一下让服务器处理文件
            await asyncio.sleep(0.5)

            # 发送textDocument/documentSymbol请求
            document_symbol_request = {
                "jsonrpc": "2.0",
                "id": self._get_next_request_id(),
                "method": "textDocument/documentSymbol",
                "params": {
                    "textDocument": {"uri": file_uri}
                },
            }

            symbol_response = await self._send_request(document_symbol_request)

            # 获取诊断信息
            diagnostics = await self._get_diagnostics_for_file(file_uri)

            # 解析符号响应
            classes, methods, fields = self._parse_document_symbols(symbol_response)

            # 发送textDocument/didClose通知以清理资源
            did_close_notification = {
                "jsonrpc": "2.0",
                "method": "textDocument/didClose",
                "params": {
                    "textDocument": {"uri": file_uri}
                },
            }
            await self._send_notification(did_close_notification)

            return {
                "classes": classes,
                "methods": methods,
                "fields": fields,
                "diagnostics": diagnostics
            }

        except Exception as e:
            logger.error(f"分析代码文件失败: {e}", exc_info=True)
            return {"classes": [], "methods": [], "fields": [], "diagnostics": []}

    def _parse_document_symbols(self, response: Optional[Dict[str, Any]]) -> tuple:
        """解析文档符号响应"""
        classes = []
        methods = []
        fields = []

        if not response or "result" not in response:
            return classes, methods, fields

        def parse_symbols(symbols: List[Dict[str, Any]], container_name: str = ""):
            for symbol in symbols:
                kind = symbol.get("kind", 0)
                name = symbol.get("name", "")
                detail = symbol.get("detail", "")
                current_full_name = f"{container_name}.{name}" if container_name else name

                # LSP符号类型: 5=Class, 6=Method, 7=Property, 8=Field, 9=Constructor, 10=Enum, 11=Interface, 12=Function
                if kind == 5:  # Class
                    classes.append({
                        "name": name,
                        "full_name": current_full_name,
                        "namespace": container_name,
                        "access_modifier": self._extract_access_modifier(detail),
                        "is_static": "static" in detail.lower(),
                        "is_abstract": "abstract" in detail.lower(),
                        "is_sealed": "sealed" in detail.lower(),
                        "base_types": [],  # 需要额外的请求来获取
                        "methods": [],
                        "fields": [],
                        "detail": detail,
                    })
                elif kind in [6, 9, 12]:  # Method, Constructor, Function
                    methods.append({
                        "name": name,
                        "full_name": current_full_name,
                        "return_type": self._extract_return_type(detail),
                        "access_modifier": self._extract_access_modifier(detail),
                        "is_async": "async" in detail.lower(),
                        "is_extension": "extension" in detail.lower(),
                        "is_override": "override" in detail.lower(),
                        "is_virtual": "virtual" in detail.lower(),
                        "is_abstract": "abstract" in detail.lower(),
                        "is_static": "static" in detail.lower(),
                        "parameters": self._extract_parameters(detail),
                        "calls": [],  # 需要额外分析
                        "detail": detail,
                    })
                elif kind in [7, 8]:  # Property, Field
                    fields.append({
                        "name": name,
                        "full_name": current_full_name,
                        "type": self._extract_type(detail),
                        "access_modifier": self._extract_access_modifier(detail),
                        "has_getter": kind == 7,  # Properties通常有getter
                        "has_setter": kind == 7,  # Properties通常有setter
                        "is_auto_property": kind == 7,
                        "is_static": "static" in detail.lower(),
                        "detail": detail,
                    })

                # 递归处理子符号
                if "children" in symbol and symbol["children"]:
                    parse_symbols(symbol["children"], current_full_name)

        try:
            parse_symbols(response["result"])
        except Exception as e:
            logger.error(f"解析符号时出错: {e}", exc_info=True)

        return classes, methods, fields

    def _extract_access_modifier(self, detail: str) -> str:
        """从detail字符串中提取访问修饰符"""
        detail_lower = detail.lower()
        if "private" in detail_lower:
            return "private"
        elif "protected" in detail_lower:
            return "protected"
        elif "internal" in detail_lower:
            return "internal"
        else:
            return "public"  # 默认为public

    def _extract_return_type(self, detail: str) -> str:
        """从detail字符串中提取返回类型"""
        try:
            # 简单的返回类型提取逻辑
            parts = detail.split()
            if len(parts) > 0:
                # 查找类型信息，通常在方法签名的开始
                for part in parts:
                    if part and not part.lower() in ["public", "private", "protected", "internal", "static", "virtual", "override", "abstract", "async"]:
                        return part
            return "void"
        except:
            return "void"

    def _extract_type(self, detail: str) -> str:
        """从detail字符串中提取类型"""
        try:
            # 简单的类型提取逻辑
            parts = detail.split()
            if len(parts) > 0:
                for part in parts:
                    if part and not part.lower() in ["public", "private", "protected", "internal", "static", "readonly", "const"]:
                        return part
            return "object"
        except:
            return "object"

    def _extract_parameters(self, detail: str) -> List[Dict[str, str]]:
        """从detail字符串中提取参数信息"""
        try:
            # 简单的参数提取逻辑
            if "(" in detail and ")" in detail:
                param_str = detail[detail.find("(") + 1:detail.rfind(")")]
                if param_str.strip():
                    # 这里可以进一步解析参数
                    return [{"name": "param", "type": "object"}]  # 占位符
            return []
        except:
            return []

    async def _get_diagnostics_for_file(self, file_uri: str) -> List[Dict[str, Any]]:
        """获取文件的诊断信息"""
        # LSP中诊断信息通常通过textDocument/publishDiagnostics通知推送
        # 这里我们返回一个基本的诊断信息
        return [{
            "type": "info",
            "message": f"文件分析完成",
            "file": file_uri,
            "line": 1,
            "column": 1,
            "severity": "info",
        }]

    async def get_diagnostics(self, file_path: str) -> List[Dict[str, Any]]:
        """获取代码诊断信息"""
        if not self.is_initialized:
            return []

        logger.info(f"获取诊断信息: {file_path}")
        file_uri = self._path_to_uri(file_path)
        return await self._get_diagnostics_for_file(file_uri)

    async def get_code_completions(self, file_path: str, line: int, character: int) -> List[str]:
        """获取代码补全建议"""
        if not self.is_initialized:
            return []

        logger.info(f"获取代码补全: {file_path} at {line}:{character}")

        try:
            file_uri = self._path_to_uri(file_path)

            completion_request = {
                "jsonrpc": "2.0",
                "id": self._get_next_request_id(),
                "method": "textDocument/completion",
                "params": {
                    "textDocument": {"uri": file_uri},
                    "position": {"line": line, "character": character}
                }
            }

            response = await self._send_request(completion_request)

            if response and "result" in response:
                items = response["result"]
                if isinstance(items, dict) and "items" in items:
                    items = items["items"]

                return [item.get("label", "") for item in items if isinstance(item, dict)]

            return []

        except Exception as e:
            logger.error(f"获取代码补全失败: {e}", exc_info=True)
            return []

    async def get_definitions(self, file_path: str, line: int, character: int) -> List[Dict[str, Any]]:
        """获取定义跳转信息"""
        if not self.is_initialized:
            return []

        logger.info(f"获取定义: {file_path} at {line}:{character}")

        try:
            file_uri = self._path_to_uri(file_path)

            definition_request = {
                "jsonrpc": "2.0",
                "id": self._get_next_request_id(),
                "method": "textDocument/definition",
                "params": {
                    "textDocument": {"uri": file_uri},
                    "position": {"line": line, "character": character}
                }
            }

            response = await self._send_request(definition_request)

            if response and "result" in response:
                locations = response["result"]
                if not isinstance(locations, list):
                    locations = [locations] if locations else []

                return [
                    {
                        "uri": loc.get("uri", ""),
                        "range": loc.get("range", {}),
                    }
                    for loc in locations if isinstance(loc, dict)
                ]

            return []

        except Exception as e:
            logger.error(f"获取定义失败: {e}", exc_info=True)
            return []

    async def get_references(self, file_path: str, line: int, character: int) -> List[Dict[str, Any]]:
        """获取引用信息"""
        if not self.is_initialized:
            return []

        logger.info(f"获取引用: {file_path} at {line}:{character}")

        try:
            file_uri = self._path_to_uri(file_path)

            references_request = {
                "jsonrpc": "2.0",
                "id": self._get_next_request_id(),
                "method": "textDocument/references",
                "params": {
                    "textDocument": {"uri": file_uri},
                    "position": {"line": line, "character": character},
                    "context": {"includeDeclaration": True}
                }
            }

            response = await self._send_request(references_request)

            if response and "result" in response:
                locations = response["result"]
                if not isinstance(locations, list):
                    locations = [locations] if locations else []

                return [
                    {
                        "uri": loc.get("uri", ""),
                        "range": loc.get("range", {}),
                    }
                    for loc in locations if isinstance(loc, dict)
                ]

            return []

        except Exception as e:
            logger.error(f"获取引用失败: {e}", exc_info=True)
            return []

    async def rename_symbol(self, file_path: str, line: int, character: int, new_name: str) -> Dict[str, Any]:
        """重命名符号"""
        if not self.is_initialized:
            return {"success": False, "error": "OmniSharp未初始化"}

        logger.info(f"重命名符号: {file_path} at {line}:{character} to {new_name}")

        try:
            file_uri = self._path_to_uri(file_path)

            rename_request = {
                "jsonrpc": "2.0",
                "id": self._get_next_request_id(),
                "method": "textDocument/rename",
                "params": {
                    "textDocument": {"uri": file_uri},
                    "position": {"line": line, "character": character},
                    "newName": new_name
                }
            }

            response = await self._send_request(rename_request)

            if response and "result" in response:
                return {"success": True, "changes": response["result"]}
            else:
                return {"success": False, "error": "重命名失败"}

        except Exception as e:
            logger.error(f"重命名符号失败: {e}", exc_info=True)
            return {"success": False, "error": str(e)}
