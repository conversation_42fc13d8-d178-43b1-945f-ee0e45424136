import asyncio
import logging
from typing import Any, Dict, List, Optional
import threading

try:
    from fastmcp import FastMCP
    FASTMCP_AVAILABLE = True
except ImportError:
    FASTMCP_AVAILABLE = False
    FastMCP = None

from pydantic import BaseModel # Keep BaseModel for internal data structures if needed

from ...config.config import get_config
from ...infrastructure.persistence.arangodb_repository import KnowledgeGraphManager
from ...interfaces.language_server.omnisharp_client import OmniSharpClient
from ...interfaces.ai.ai_manager import AIManager

logger = logging.getLogger(__name__)


class MCPService:
    """MCP 服务，提供工具接口"""

    def __init__(
        self,
        knowledge_graph_manager: KnowledgeGraphManager,
        omnisharp_client: OmniSharpClient,
        ai_manager: AIManager,
    ):
        self.config = get_config()
        self.knowledge_graph_manager = knowledge_graph_manager
        self.omnisharp_client = omnisharp_client
        self.ai_manager = ai_manager

        if FASTMCP_AVAILABLE:
            self.mcp = FastMCP(name="CSharpAssistant") # Initialize FastMCP
            self._register_tools()
        else:
            self.mcp = None
            logger.warning("FastMCP不可用，MCP服务将以简化模式运行")

    def _register_tools(self):
        """注册MCP工具"""
        if not self.mcp:
            return

        # Register tools using @self.mcp.tool()
        @self.mcp.tool(
            name="initialize_project",
            description="初始化C#项目并开始监控。分析项目结构，建立知识图谱，启动文件监控。"
        )
        async def initialize_project(project_path: str, project_name: str):
            """初始化项目"""
            try:
                logger.info(f"初始化项目: {project_name} at {project_path}")

                # 验证项目路径
                if not os.path.exists(project_path):
                    return {"success": False, "error": f"项目路径不存在: {project_path}"}

                # 更新配置
                import os
                os.environ["PROJECT_PATH"] = project_path
                os.environ["PROJECT_NAME"] = project_name

                # 重新加载配置
                from ...config.config import get_config
                config = get_config()

                # 初始化知识图谱
                await self.knowledge_graph_manager.initialize_schema()

                # 启动OmniSharp（如果启用）
                if config.language_server_enabled:
                    await self.omnisharp_client.start(project_path)

                # 进行初始代码分析
                import glob
                cs_files = glob.glob(os.path.join(project_path, "**/*.cs"), recursive=True)

                analysis_results = {"files": []}
                for file_path in cs_files[:10]:  # 限制初始分析文件数量
                    try:
                        analysis_result = await self.omnisharp_client.analyze_code(file_path)
                        if analysis_result:
                            analysis_results["files"].append({"path": file_path, **analysis_result})
                    except Exception as e:
                        logger.error(f"分析文件失败 {file_path}: {e}")
                        continue

                # 更新知识图谱
                if analysis_results["files"]:
                    await self.knowledge_graph_manager.update_from_analysis(
                        project_name, analysis_results
                    )

                return {
                    "success": True,
                    "message": f"项目 {project_name} 初始化成功",
                    "files_analyzed": len(analysis_results["files"]),
                    "total_cs_files": len(cs_files)
                }

            except Exception as e:
                logger.error(f"初始化项目失败: {e}", exc_info=True)
                return {"success": False, "error": str(e)}

        @self.mcp.tool(
            name="query_project",
            description="查询项目知识图谱。根据问题搜索相关的类、方法、字段等信息，并提供AI分析。"
        )
        async def query_project(project_name: str, question: str):
            """查询项目知识图谱"""
            try:
                logger.info(f"查询项目 {project_name}: {question}")

                # 从知识图谱获取上下文
                context = await self.knowledge_graph_manager.query_context(question)

                if not context:
                    return {
                        "success": True,
                        "answer": "未找到相关信息",
                        "context": [],
                        "suggestions": ["请检查查询关键词", "确保项目已正确初始化"]
                    }

                # 获取AI服务
                ai_service = self.ai_manager.get_service()
                if not ai_service:
                    return {
                        "success": True,
                        "answer": "AI服务不可用，但找到了相关信息",
                        "context": context,
                        "suggestions": []
                    }

                # 使用AI分析上下文并生成答案
                ai_context = {
                    "question": question,
                    "project_name": project_name,
                    "knowledge_graph_context": context
                }

                suggestions = await ai_service.get_suggestions(
                    context=ai_context,
                    query=question
                )

                return {
                    "success": True,
                    "answer": suggestions[0] if suggestions else "AI分析完成，但未生成具体建议",
                    "context": context,
                    "suggestions": suggestions[1:] if len(suggestions) > 1 else []
                }

            except Exception as e:
                logger.error(f"查询项目失败: {e}", exc_info=True)
                return {"success": False, "error": str(e)}

        @self.mcp.tool(
            name="get_suggestions",
            description="基于上下文获取代码改进建议。分析当前代码状态并提供具体的改进建议。"
        )
        async def get_suggestions(project_name: str, context: str, file_path: str = None):
            """获取代码建议"""
            try:
                logger.info(f"获取建议 for {project_name} with context: {context}")

                # 构建上下文信息
                suggestion_context = {
                    "project_name": project_name,
                    "user_context": context
                }

                # 如果提供了文件路径，分析该文件
                if file_path and os.path.exists(file_path):
                    try:
                        analysis_result = await self.omnisharp_client.analyze_code(file_path)
                        if analysis_result:
                            suggestion_context["file_analysis"] = analysis_result
                            suggestion_context["file_path"] = file_path
                    except Exception as e:
                        logger.warning(f"分析文件失败 {file_path}: {e}")

                # 从知识图谱获取相关信息
                if context:
                    kg_context = await self.knowledge_graph_manager.query_context(context)
                    suggestion_context["knowledge_graph"] = kg_context

                # 获取AI建议
                ai_service = self.ai_manager.get_service()
                if not ai_service:
                    return {
                        "success": False,
                        "error": "AI服务不可用",
                        "suggestions": ["请检查AI服务配置"]
                    }

                suggestions = await ai_service.get_suggestions(
                    context=suggestion_context,
                    query=context
                )

                return {
                    "success": True,
                    "suggestions": suggestions,
                    "context_used": {
                        "has_file_analysis": "file_analysis" in suggestion_context,
                        "has_knowledge_graph": "knowledge_graph" in suggestion_context,
                        "kg_results_count": len(suggestion_context.get("knowledge_graph", []))
                    }
                }

            except Exception as e:
                logger.error(f"获取建议失败: {e}", exc_info=True)
                return {"success": False, "error": str(e)}

        @self.mcp.tool(
            name="analyze_file",
            description="分析指定的C#文件，返回详细的代码结构信息。"
        )
        async def analyze_file(file_path: str):
            """分析文件"""
            try:
                logger.info(f"分析文件: {file_path}")

                if not os.path.exists(file_path):
                    return {"success": False, "error": f"文件不存在: {file_path}"}

                if not file_path.endswith('.cs'):
                    return {"success": False, "error": "只支持C#文件(.cs)"}

                # 使用OmniSharp分析文件
                analysis_result = await self.omnisharp_client.analyze_code(file_path)

                if not analysis_result:
                    return {"success": False, "error": "文件分析失败"}

                # 统计信息
                stats = {
                    "classes_count": len(analysis_result.get("classes", [])),
                    "methods_count": len(analysis_result.get("methods", [])),
                    "fields_count": len(analysis_result.get("fields", [])),
                    "diagnostics_count": len(analysis_result.get("diagnostics", []))
                }

                return {
                    "success": True,
                    "file_path": file_path,
                    "analysis": analysis_result,
                    "statistics": stats
                }

            except Exception as e:
                logger.error(f"分析文件失败: {e}", exc_info=True)
                return {"success": False, "error": str(e)}

        @self.mcp.tool(
            name="get_class_details",
            description="获取指定类的详细信息，包括方法、字段和继承关系。"
        )
        async def get_class_details(class_name: str):
            """获取类详情"""
            try:
                logger.info(f"获取类详情: {class_name}")

                details = await self.knowledge_graph_manager.get_class_details(class_name)

                if not details:
                    return {
                        "success": False,
                        "error": f"未找到类: {class_name}",
                        "suggestions": ["检查类名拼写", "确保项目已初始化"]
                    }

                return {
                    "success": True,
                    "class_details": details
                }

            except Exception as e:
                logger.error(f"获取类详情失败: {e}", exc_info=True)
                return {"success": False, "error": str(e)}

        @self.mcp.tool(
            name="get_method_calls",
            description="获取方法的调用关系，包括调用了哪些方法以及被哪些方法调用。"
        )
        async def get_method_calls(method_name: str):
            """获取方法调用关系"""
            try:
                logger.info(f"获取方法调用关系: {method_name}")

                call_info = await self.knowledge_graph_manager.get_method_calls(method_name)

                if not call_info:
                    return {
                        "success": False,
                        "error": f"未找到方法: {method_name}",
                        "suggestions": ["检查方法名拼写", "使用完整的方法名"]
                    }

                return {
                    "success": True,
                    "call_relationships": call_info
                }

            except Exception as e:
                logger.error(f"获取方法调用关系失败: {e}", exc_info=True)
                return {"success": False, "error": str(e)}

        @self.mcp.tool(
            name="report_error",
            description="报告代码错误并获取修复建议。提供错误信息和上下文以获得AI驱动的修复建议。"
        )
        async def report_error(project_name: str, error_info: Dict[str, Any]):
            """报告错误"""
            try:
                logger.info(f"报告错误 for {project_name}: {error_info}")

                # 构建错误上下文
                error_context = {
                    "project_name": project_name,
                    "error_info": error_info
                }

                # 如果有文件路径，获取文件分析
                file_path = error_info.get("file")
                if file_path and os.path.exists(file_path):
                    try:
                        analysis = await self.omnisharp_client.analyze_code(file_path)
                        if analysis:
                            error_context["file_analysis"] = analysis
                    except Exception as e:
                        logger.warning(f"分析错误文件失败: {e}")

                # 获取AI修复建议
                ai_service = self.ai_manager.get_service()
                if not ai_service:
                    return {
                        "success": False,
                        "error": "AI服务不可用",
                        "fix_suggestion": "请手动检查错误信息"
                    }

                fix_result = await ai_service.report_error(
                    error_info=error_info,
                    context=error_context
                )

                return {
                    "success": True,
                    "fix_suggestion": fix_result.get("fix_suggestion", ""),
                    "confidence": fix_result.get("confidence", "medium"),
                    "error_type": error_info.get("type", "unknown")
                }

            except Exception as e:
                logger.error(f"报告错误失败: {e}", exc_info=True)
                return {"success": False, "error": str(e)}

        @self.mcp.tool(
            name="fix_error",
            description="应用错误修复。基于错误报告和修复建议生成具体的代码修复方案。"
        )
        async def fix_error(project_name: str, error_info: Dict[str, Any], fix_suggestion: Dict[str, Any]):
            """应用错误修复"""
            try:
                logger.info(f"修复错误 for {project_name}")

                # 构建修复上下文
                fix_context = {
                    "project_name": project_name,
                    "error_info": error_info,
                    "fix_suggestion": fix_suggestion
                }

                # 获取AI服务
                ai_service = self.ai_manager.get_service()
                if not ai_service:
                    return {
                        "success": False,
                        "error": "AI服务不可用",
                        "fix_plan": "请手动应用修复建议"
                    }

                # 生成修复方案
                fix_result = await ai_service.fix_error(
                    error_info=error_info,
                    fix_suggestion=fix_suggestion,
                    context=fix_context
                )

                return {
                    "success": True,
                    "status": "fix_generated",
                    "fix_plan": fix_result.get("fix_plan", ""),
                    "changes": fix_result.get("changes", {}),
                    "verification_steps": fix_result.get("verification_steps", [])
                }

            except Exception as e:
                logger.error(f"修复错误失败: {e}", exc_info=True)
                return {"success": False, "error": str(e)}

        @self.mcp.tool(
            name="search_code",
            description="在项目中搜索代码。支持按类名、方法名、字段名等进行搜索。"
        )
        async def search_code(query: str, search_type: str = "all"):
            """搜索代码"""
            try:
                logger.info(f"搜索代码: {query} (类型: {search_type})")

                # 从知识图谱搜索
                results = await self.knowledge_graph_manager.query_context(query, limit=20)

                # 按类型过滤结果
                if search_type != "all":
                    results = [r for r in results if r.get("type") == search_type]

                # 按类型分组
                grouped_results = {
                    "classes": [r for r in results if r.get("type") == "class"],
                    "methods": [r for r in results if r.get("type") == "method"],
                    "fields": [r for r in results if r.get("type") == "field"]
                }

                return {
                    "success": True,
                    "query": query,
                    "search_type": search_type,
                    "total_results": len(results),
                    "results": grouped_results
                }

            except Exception as e:
                logger.error(f"搜索代码失败: {e}", exc_info=True)
                return {"success": False, "error": str(e)}

        @self.mcp.tool(
            name="get_project_stats",
            description="获取项目统计信息，包括文件数量、类数量、方法数量等。"
        )
        async def get_project_stats(project_name: str):
            """获取项目统计"""
            try:
                logger.info(f"获取项目统计: {project_name}")

                # 使用AQL查询统计信息
                if not self.knowledge_graph_manager.graph:
                    return {"success": False, "error": "知识图谱未初始化"}

                stats_query = """
                LET project_count = LENGTH(FOR p IN Project RETURN p)
                LET file_count = LENGTH(FOR f IN File RETURN f)
                LET class_count = LENGTH(FOR c IN Class RETURN c)
                LET method_count = LENGTH(FOR m IN Method RETURN m)
                LET field_count = LENGTH(FOR f IN Field RETURN f)
                LET parameter_count = LENGTH(FOR p IN Parameter RETURN p)

                RETURN {
                    projects: project_count,
                    files: file_count,
                    classes: class_count,
                    methods: method_count,
                    fields: field_count,
                    parameters: parameter_count
                }
                """

                cursor = self.knowledge_graph_manager.db.aql.execute(stats_query)
                stats = [doc for doc in cursor][0]

                return {
                    "success": True,
                    "project_name": project_name,
                    "statistics": stats,
                    "timestamp": self.knowledge_graph_manager._get_timestamp()
                }

            except Exception as e:
                logger.error(f"获取项目统计失败: {e}", exc_info=True)
                return {"success": False, "error": str(e)}

    async def start(self):
        """启动 MCP 服务"""
        if not self.mcp:
            logger.info("MCP服务在简化模式下运行，跳过启动")
            return

        host = self.config.mcp_host
        port = self.config.mcp_port
        logger.info(f"正在启动 MCP 服务，监听地址: http://{host}:{port}")
        try:
            await self.mcp.start(host=host, port=port)
        except Exception as e:
            logger.error(f"启动MCP服务失败: {e}")

    async def stop(self):
        """停止 MCP 服务"""
        if not self.mcp:
            logger.info("MCP服务在简化模式下运行，跳过停止")
            return

        logger.info("正在停止 MCP 服务")
        try:
            await self.mcp.stop()
        except Exception as e:
            logger.error(f"停止MCP服务失败: {e}")
        logger.info("MCP 服务已停止")
