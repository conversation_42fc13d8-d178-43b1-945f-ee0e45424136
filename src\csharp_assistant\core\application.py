"""
应用核心
"""

import asyncio
import logging
import glob
import os

from ..infrastructure.adapters.file_watcher import FileWatcher
from ..infrastructure.persistence import KnowledgeGraphRepository
from ..interfaces.language_server.omnisharp_client import OmniSharpClient
from ..interfaces.ai.ai_manager import AIManager
from ..infrastructure.adapters.mcp_service import MCPService
from ..config.config import get_config

logger = logging.getLogger(__name__)


class Application:
    """应用核心类"""

    def __init__(self):
        self.config = get_config()
        self.knowledge_graph_manager = KnowledgeGraphRepository()
        self.omnisharp_client = OmniSharpClient()
        self.ai_manager = AIManager()
        self.file_watcher = FileWatcher(change_callback=self._handle_file_change)
        self.mcp_service = MCPService(
            knowledge_graph_manager=self.knowledge_graph_manager,
            omnisharp_client=self.omnisharp_client,
            ai_manager=self.ai_manager,
        )

    async def start(self):
        """启动应用"""
        logger.info("应用已启动")
        await self.knowledge_graph_manager.initialize_schema()
        await self.omnisharp_client.start(self.config.project_path)

        # Perform initial code analysis and populate knowledge graph
        if self.config.language_server_enabled:
            logger.info("开始进行初始代码分析并填充知识图谱")
            csharp_files = glob.glob(os.path.join(self.config.project_path, "**", "*.cs"), recursive=True)
            project_analysis_results = {"files": []}
            for file_path in csharp_files:
                try:
                    logger.info(f"分析文件: {file_path}")
                    analysis_result = await self.omnisharp_client.analyze_code(file_path)
                    if analysis_result:
                        project_analysis_results["files"].append({"path": file_path, **analysis_result})
                except Exception as e:
                    logger.error(f"分析文件失败 {file_path}: {e}", exc_info=True)
                    continue

            if project_analysis_results["files"]:
                try:
                    await self.knowledge_graph_manager.update_from_analysis(
                        self.config.project_name,
                        project_analysis_results
                    )
                    logger.info("知识图谱填充完成")

                    # 使用AI服务分析代码
                    await self._perform_ai_analysis(project_analysis_results)

                except Exception as e:
                    logger.error(f"更新知识图谱失败: {e}", exc_info=True)
            else:
                logger.warning("未找到 C# 文件进行分析或分析结果为空")

        await self.mcp_service.start()
        await self.file_watcher.start()

    async def _handle_file_change(self, file_path: str, event_type: str):
        """处理文件变更事件"""
        try:
            logger.info(f"处理文件变更: {file_path} ({event_type})")

            if event_type == "deleted":
                # 处理文件删除
                await self._handle_file_deletion(file_path)
            elif event_type in ["modified", "created"]:
                # 处理文件修改或创建
                await self._handle_file_update(file_path)

        except Exception as e:
            logger.error(f"处理文件变更时出错: {e}", exc_info=True)

    async def _handle_file_deletion(self, file_path: str):
        """处理文件删除"""
        try:
            # 从知识图谱中删除相关实体
            # 这里需要根据文件路径查找相关的代码实体并删除
            logger.info(f"从知识图谱中删除文件相关实体: {file_path}")
            # TODO: 实现具体的删除逻辑

        except Exception as e:
            logger.error(f"处理文件删除时出错: {e}", exc_info=True)

    async def _handle_file_update(self, file_path: str):
        """处理文件更新（修改或创建）"""
        try:
            if not self.config.language_server_enabled:
                logger.debug("语言服务器未启用，跳过文件分析")
                return

            # 分析文件
            logger.info(f"重新分析文件: {file_path}")
            analysis_result = await self.omnisharp_client.analyze_code(file_path)

            if analysis_result:
                # 更新知识图谱
                project_analysis_results = {
                    "files": [{"path": file_path, **analysis_result}]
                }

                await self.knowledge_graph_manager.update_from_analysis(
                    self.config.project_name,
                    project_analysis_results
                )

                # 使用AI服务分析单个文件
                await self._perform_ai_analysis(project_analysis_results)

                logger.info(f"已更新知识图谱: {file_path}")
            else:
                logger.warning(f"文件分析结果为空: {file_path}")

        except Exception as e:
            logger.error(f"处理文件更新时出错: {e}", exc_info=True)

    async def _perform_ai_analysis(self, analysis_results: dict):
        """使用AI服务分析代码"""
        try:
            ai_service = self.ai_manager.get_service()
            if not ai_service:
                logger.warning("AI服务不可用，跳过AI分析")
                return

            logger.info("开始使用AI服务分析代码")

            # 对每个文件进行AI分析
            for file_data in analysis_results.get("files", []):
                try:
                    file_path = file_data.get("path", "")
                    logger.info(f"AI分析文件: {file_path}")

                    # 构建分析上下文
                    analysis_context = {
                        "file_path": file_path,
                        "classes": file_data.get("classes", []),
                        "methods": file_data.get("methods", []),
                        "fields": file_data.get("fields", []),
                        "diagnostics": file_data.get("diagnostics", [])
                    }

                    # 调用AI分析
                    ai_result = await ai_service.analyze_code(analysis_context)
                    logger.info(f"AI分析完成: {file_path}")
                    logger.debug(f"AI分析结果: {ai_result}")

                except Exception as e:
                    logger.error(f"AI分析文件失败 {file_path}: {e}", exc_info=True)
                    continue

            logger.info("AI代码分析完成")

        except Exception as e:
            logger.error(f"AI分析过程失败: {e}", exc_info=True)

    async def stop(self):
        """停止应用"""
        try:
            await self.file_watcher.stop()
            await self.omnisharp_client.stop()
            await self.mcp_service.stop()
            self.knowledge_graph_manager.close()
            logger.info("应用已停止")
        except Exception as e:
            logger.error(f"停止应用时出错: {e}", exc_info=True)
