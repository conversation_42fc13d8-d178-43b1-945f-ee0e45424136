#!/usr/bin/env python3
"""
C# Assistant 完整演示脚本

这个脚本演示了C# Assistant的所有主要功能，包括：
1. 项目初始化
2. 代码分析
3. 知识图谱查询
4. AI建议生成
5. 错误报告和修复
"""

import asyncio
import os
import sys
import json
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.csharp_assistant.core.application import Application
from src.csharp_assistant.config.config import get_config

console = Console()


class CSharpAssistantDemo:
    """C# Assistant演示类"""
    
    def __init__(self):
        self.app = None
        self.config = None
        
    async def setup(self):
        """设置演示环境"""
        console.print(Panel.fit(
            "C# Assistant 完整功能演示",
            style="bold blue",
            border_style="blue"
        ))
        
        # 设置环境变量
        os.environ["PROJECT_PATH"] = str(project_root)
        os.environ["PROJECT_NAME"] = "CSharp-Assistant-Demo"
        os.environ["AI_PROVIDER"] = "ollama"
        os.environ["LANGUAGE_SERVER_ENABLED"] = "false"  # 简化演示
        
        # 获取配置
        self.config = get_config()
        console.print(f"📁 项目路径: {self.config.project_path}")
        console.print(f"📝 项目名称: {self.config.project_name}")
        console.print(f"🤖 AI提供商: {self.config.ai_provider}")
        console.print()
        
        # 创建应用实例
        self.app = Application()
        
    async def demo_knowledge_graph(self):
        """演示知识图谱功能"""
        console.print(Panel("🗄️ 知识图谱演示", style="bold green"))
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task("初始化知识图谱...", total=None)
                
                # 初始化知识图谱
                await self.app.knowledge_graph_manager.initialize_schema()
                progress.update(task, description="✅ 知识图谱初始化完成")
            
            console.print("[green]✓ 知识图谱初始化成功[/green]")
            
            # 模拟添加一些测试数据
            test_analysis = {
                "files": [
                    {
                        "path": "TestClass.cs",
                        "classes": [
                            {
                                "name": "TestClass",
                                "namespace": "Demo",
                                "full_name": "Demo.TestClass",
                                "access_modifier": "public",
                                "is_static": False,
                                "is_abstract": False,
                                "is_sealed": False
                            }
                        ],
                        "methods": [
                            {
                                "name": "TestMethod",
                                "full_name": "Demo.TestClass.TestMethod",
                                "return_type": "string",
                                "access_modifier": "public",
                                "is_static": False,
                                "is_async": True,
                                "parameters": [
                                    {"name": "input", "type": "string"}
                                ]
                            }
                        ],
                        "fields": [
                            {
                                "name": "testField",
                                "full_name": "Demo.TestClass.testField",
                                "type": "int",
                                "access_modifier": "private",
                                "is_static": False
                            }
                        ],
                        "diagnostics": []
                    }
                ]
            }
            
            # 更新知识图谱
            await self.app.knowledge_graph_manager.update_from_analysis(
                self.config.project_name, test_analysis
            )
            console.print("[green]✓ 测试数据添加成功[/green]")
            
        except Exception as e:
            console.print(f"[red]✗ 知识图谱演示失败: {e}[/red]")
            
    async def demo_query(self):
        """演示查询功能"""
        console.print(Panel("🔍 查询功能演示", style="bold yellow"))
        
        try:
            # 测试查询
            queries = ["TestClass", "TestMethod", "string"]
            
            for query in queries:
                console.print(f"🔍 查询: {query}")
                results = await self.app.knowledge_graph_manager.query_context(query)
                
                if results:
                    table = Table(title=f"查询结果: {query}")
                    table.add_column("类型", style="cyan")
                    table.add_column("名称", style="green")
                    table.add_column("详情", style="yellow")
                    
                    for result in results[:5]:  # 限制显示数量
                        table.add_row(
                            result.get("type", "unknown"),
                            result.get("name", ""),
                            result.get("full_name", "")
                        )
                    
                    console.print(table)
                else:
                    console.print(f"[yellow]未找到 '{query}' 的相关结果[/yellow]")
                console.print()
                
        except Exception as e:
            console.print(f"[red]✗ 查询演示失败: {e}[/red]")
            
    async def demo_ai_service(self):
        """演示AI服务功能"""
        console.print(Panel("🤖 AI服务演示", style="bold magenta"))
        
        try:
            ai_service = self.app.ai_manager.get_service()
            
            if not ai_service:
                console.print("[yellow]⚠️ AI服务未配置，跳过AI演示[/yellow]")
                return
            
            # 测试AI建议
            console.print("🤖 获取代码建议...")
            context = {
                "project_name": self.config.project_name,
                "current_class": "TestClass",
                "current_method": "TestMethod"
            }
            
            suggestions = await ai_service.get_suggestions(
                context=context,
                query="如何改进这个类的设计？"
            )
            
            if suggestions:
                console.print("[green]✓ AI建议生成成功[/green]")
                for i, suggestion in enumerate(suggestions[:3], 1):
                    console.print(f"{i}. {suggestion}")
            else:
                console.print("[yellow]⚠️ 未生成AI建议[/yellow]")
                
            console.print()
            
            # 测试错误报告
            console.print("🤖 测试错误报告...")
            error_info = {
                "type": "compilation_error",
                "message": "CS0103: The name 'undefinedVariable' does not exist",
                "file": "TestClass.cs",
                "line": 15,
                "column": 10
            }
            
            fix_result = await ai_service.report_error(error_info, context)
            
            if fix_result and fix_result.get("fix_suggestion"):
                console.print("[green]✓ 错误修复建议生成成功[/green]")
                console.print(f"修复建议: {fix_result['fix_suggestion'][:200]}...")
            else:
                console.print("[yellow]⚠️ 未生成修复建议[/yellow]")
                
        except Exception as e:
            console.print(f"[red]✗ AI服务演示失败: {e}[/red]")
            
    async def demo_stats(self):
        """演示统计功能"""
        console.print(Panel("📊 统计信息演示", style="bold cyan"))
        
        try:
            # 获取项目统计
            if self.app.knowledge_graph_manager.graph:
                stats_query = """
                LET project_count = LENGTH(FOR p IN Project RETURN p)
                LET file_count = LENGTH(FOR f IN File RETURN f)
                LET class_count = LENGTH(FOR c IN Class RETURN c)
                LET method_count = LENGTH(FOR m IN Method RETURN m)
                LET field_count = LENGTH(FOR f IN Field RETURN f)
                
                RETURN {
                    projects: project_count,
                    files: file_count,
                    classes: class_count,
                    methods: method_count,
                    fields: field_count
                }
                """
                
                cursor = self.app.knowledge_graph_manager.db.aql.execute(stats_query)
                stats = [doc for doc in cursor][0]
                
                # 显示统计表格
                table = Table(title="项目统计信息")
                table.add_column("项目", style="cyan")
                table.add_column("数量", style="green", justify="right")
                
                table.add_row("项目", str(stats["projects"]))
                table.add_row("文件", str(stats["files"]))
                table.add_row("类", str(stats["classes"]))
                table.add_row("方法", str(stats["methods"]))
                table.add_row("字段", str(stats["fields"]))
                
                console.print(table)
            else:
                console.print("[yellow]⚠️ 知识图谱未初始化[/yellow]")
                
        except Exception as e:
            console.print(f"[red]✗ 统计演示失败: {e}[/red]")
            
    async def cleanup(self):
        """清理资源"""
        if self.app:
            try:
                await self.app.stop()
                console.print("[green]✓ 资源清理完成[/green]")
            except Exception as e:
                console.print(f"[yellow]⚠️ 清理时出现警告: {e}[/yellow]")
                
    async def run_demo(self):
        """运行完整演示"""
        try:
            await self.setup()
            
            # 运行各个演示模块
            await self.demo_knowledge_graph()
            await self.demo_query()
            await self.demo_ai_service()
            await self.demo_stats()
            
            console.print(Panel.fit(
                "🎉 演示完成！所有功能运行正常。",
                style="bold green",
                border_style="green"
            ))
            
        except KeyboardInterrupt:
            console.print("\n[yellow]演示被用户中断[/yellow]")
        except Exception as e:
            console.print(f"[red]演示失败: {e}[/red]")
            import traceback
            traceback.print_exc()
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    demo = CSharpAssistantDemo()
    await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
