#!/usr/bin/env python3
"""
知识图谱诊断脚本

检查ArangoDB中的知识图谱数据，分析数据存储情况
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.csharp_assistant.infrastructure.persistence.arangodb_repository import KnowledgeGraphManager
from src.csharp_assistant.config.config import get_config

async def diagnose_knowledge_graph():
    """诊断知识图谱数据"""
    print("🔍 开始诊断知识图谱数据...")
    
    try:
        config = get_config()
        repo = KnowledgeGraphManager()
        
        # 初始化知识图谱
        await repo.initialize_schema()
        print("✅ 成功连接到ArangoDB")
        
        # 检查数据库和集合
        print("\n📊 数据库信息:")
        db = repo.db
        
        # 列出所有集合
        collections = [col['name'] for col in db.collections()]
        print(f"集合列表: {collections}")
        
        # 检查项目集合
        if 'Project' in collections:
            projects_col = db.collection('Project')
            project_count = projects_col.count()
            print(f"项目数量: {project_count}")

            # 获取项目详情
            for project in projects_col.all():
                print(f"项目: {project}")

        # 检查文件集合
        if 'File' in collections:
            files_col = db.collection('File')
            file_count = files_col.count()
            print(f"文件数量: {file_count}")

            # 获取文件详情
            for file_doc in files_col.all():
                print(f"文件: {file_doc}")

        # 检查类集合
        if 'Class' in collections:
            classes_col = db.collection('Class')
            class_count = classes_col.count()
            print(f"类数量: {class_count}")

            # 获取类详情
            for class_doc in classes_col.all():
                print(f"类: {class_doc}")

        # 检查方法集合
        if 'Method' in collections:
            methods_col = db.collection('Method')
            method_count = methods_col.count()
            print(f"方法数量: {method_count}")

            # 获取方法详情
            for method_doc in methods_col.all():
                print(f"方法: {method_doc}")

        # 检查字段集合
        if 'Field' in collections:
            fields_col = db.collection('Field')
            field_count = fields_col.count()
            print(f"字段数量: {field_count}")

            # 获取字段详情
            for field_doc in fields_col.all():
                print(f"字段: {field_doc}")

        # 检查参数集合
        if 'Parameter' in collections:
            params_col = db.collection('Parameter')
            param_count = params_col.count()
            print(f"参数数量: {param_count}")

            # 获取参数详情
            for param_doc in params_col.all():
                print(f"参数: {param_doc}")
        
        # 检查关系集合
        edge_collections = [col for col in collections if col.endswith('_edges')]
        print(f"\n🔗 关系集合: {edge_collections}")
        
        for edge_col_name in edge_collections:
            edge_col = db.collection(edge_col_name)
            edge_count = edge_col.count()
            print(f"{edge_col_name} 关系数量: {edge_count}")
            
            # 获取关系详情
            for edge_doc in edge_col.all():
                print(f"关系: {edge_doc}")
        
        # 检查图
        graphs = db.graphs()
        print(f"\n📈 图列表: {[g['name'] for g in graphs]}")
        
        for graph_info in graphs:
            graph_name = graph_info['name']
            print(f"图: {graph_name}")
            
            if graph_name == 'knowledge_graph':
                graph = db.graph('knowledge_graph')
                vertex_collections = graph.vertex_collections()
                edge_definitions = graph.edge_definitions()
                
                print(f"  顶点集合: {vertex_collections}")
                print(f"  边定义: {edge_definitions}")
        
        print("\n✅ 知识图谱诊断完成")
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # KnowledgeGraphManager 不需要显式断开连接
        pass

async def test_knowledge_graph_operations():
    """测试知识图谱操作"""
    print("\n🧪 测试知识图谱操作...")

    try:
        config = get_config()
        repo = KnowledgeGraphManager()

        # 初始化知识图谱
        await repo.initialize_schema()

        # 直接查询数据库
        db = repo.db

        # 查询项目
        project_col = db.collection('Project')
        projects = list(project_col.all())
        print(f"项目数量: {len(projects)}")
        for project in projects:
            print(f"  项目: {project}")

        # 查询文件
        file_col = db.collection('File')
        files = list(file_col.all())
        print(f"文件数量: {len(files)}")
        for file_doc in files:
            print(f"  文件: {file_doc}")

        # 查询类
        class_col = db.collection('Class')
        classes = list(class_col.all())
        print(f"类数量: {len(classes)}")
        for class_doc in classes:
            print(f"  类: {class_doc}")

        # 查询方法
        method_col = db.collection('Method')
        methods = list(method_col.all())
        print(f"方法数量: {len(methods)}")
        for method_doc in methods:
            print(f"  方法: {method_doc}")

        # 查询字段
        field_col = db.collection('Field')
        fields = list(field_col.all())
        print(f"字段数量: {len(fields)}")
        for field_doc in fields:
            print(f"  字段: {field_doc}")

        print("✅ 知识图谱操作测试完成")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # KnowledgeGraphManager 不需要显式断开连接
        pass

async def main():
    """主函数"""
    await diagnose_knowledge_graph()
    await test_knowledge_graph_operations()

if __name__ == "__main__":
    asyncio.run(main())
