#!/usr/bin/env python3
"""
OmniSharp修复验证测试脚本

测试OmniSharp的初始化、LSP通信和整个系统的功能
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.csharp_assistant.interfaces.language_server.omnisharp_client import OmniSharpClient
from src.csharp_assistant.config.config import get_config
from src.csharp_assistant.core.application import Application

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)8s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


class OmniSharpTester:
    """OmniSharp测试器"""
    
    def __init__(self):
        self.config = get_config()
        self.omnisharp_client = None
        self.application = None
        
    async def test_dotnet_environment(self):
        """测试.NET环境"""
        logger.info("=== 测试.NET环境 ===")
        
        try:
            # 检查dotnet命令
            process = await asyncio.create_subprocess_exec(
                "dotnet", "--version",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                version = stdout.decode().strip()
                logger.info(f"✅ .NET SDK版本: {version}")
                return True
            else:
                logger.error(f"❌ .NET SDK不可用: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"❌ .NET环境检查失败: {e}")
            return False
    
    async def test_project_build(self):
        """测试项目构建"""
        logger.info("=== 测试项目构建 ===")
        
        try:
            project_path = self.config.project_path
            sln_files = list(Path(project_path).glob("*.sln"))
            
            if not sln_files:
                logger.warning("⚠️ 未找到.sln文件，跳过构建测试")
                return True
                
            sln_file = str(sln_files[0])
            logger.info(f"构建项目: {sln_file}")
            
            process = await asyncio.create_subprocess_exec(
                "dotnet", "build", sln_file,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.info("✅ 项目构建成功")
                return True
            else:
                logger.error(f"❌ 项目构建失败: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 项目构建测试失败: {e}")
            return False
    
    async def test_omnisharp_startup(self):
        """测试OmniSharp启动"""
        logger.info("=== 测试OmniSharp启动 ===")
        
        try:
            self.omnisharp_client = OmniSharpClient()
            
            # 测试启动
            await self.omnisharp_client.start(self.config.project_path)
            
            if self.omnisharp_client.is_initialized:
                logger.info("✅ OmniSharp初始化成功")
                return True
            else:
                logger.warning("⚠️ OmniSharp启动但未完全初始化")
                return False
                
        except Exception as e:
            logger.error(f"❌ OmniSharp启动测试失败: {e}")
            return False
    
    async def test_code_analysis(self):
        """测试代码分析"""
        logger.info("=== 测试代码分析 ===")
        
        try:
            if not self.omnisharp_client:
                logger.error("❌ OmniSharp客户端未初始化")
                return False
            
            # 查找C#文件
            cs_files = list(Path(self.config.project_path).rglob("*.cs"))
            if not cs_files:
                logger.warning("⚠️ 未找到C#文件")
                return True
            
            # 测试分析第一个文件
            test_file = str(cs_files[0])
            logger.info(f"分析文件: {test_file}")
            
            result = await self.omnisharp_client.analyze_code(test_file)
            
            if result:
                classes_count = len(result.get("classes", []))
                methods_count = len(result.get("methods", []))
                fields_count = len(result.get("fields", []))
                
                logger.info(f"✅ 代码分析成功: {classes_count}个类, {methods_count}个方法, {fields_count}个字段")
                return True
            else:
                logger.warning("⚠️ 代码分析返回空结果")
                return False
                
        except Exception as e:
            logger.error(f"❌ 代码分析测试失败: {e}")
            return False
    
    async def test_full_application(self):
        """测试完整应用程序"""
        logger.info("=== 测试完整应用程序 ===")
        
        try:
            self.application = Application()
            
            # 启动应用程序
            await self.application.start()
            logger.info("✅ 应用程序启动成功")
            
            # 等待一段时间让初始化完成
            await asyncio.sleep(5)
            
            # 检查AI管理器
            ai_service = self.application.ai_manager.get_service()
            if ai_service:
                logger.info("✅ AI服务可用")
            else:
                logger.warning("⚠️ AI服务不可用")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 完整应用程序测试失败: {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        logger.info("=== 清理资源 ===")
        
        try:
            if self.omnisharp_client:
                await self.omnisharp_client.stop()
                logger.info("✅ OmniSharp客户端已停止")
            
            if self.application:
                await self.application.stop()
                logger.info("✅ 应用程序已停止")
                
        except Exception as e:
            logger.error(f"❌ 清理资源失败: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始OmniSharp修复验证测试")
        
        tests = [
            ("测试.NET环境", self.test_dotnet_environment),
            ("测试项目构建", self.test_project_build),
            ("测试OmniSharp启动", self.test_omnisharp_startup),
            ("测试代码分析", self.test_code_analysis),
            ("测试完整应用程序", self.test_full_application),
        ]
        
        results = []
        
        try:
            for test_name, test_func in tests:
                logger.info(f"\n--- {test_name} ---")
                result = await test_func()
                results.append((test_name, result))
                
                if not result:
                    logger.warning(f"⚠️ {test_name} 失败，继续下一个测试")
        
        finally:
            await self.cleanup()
        
        # 输出测试结果
        logger.info("\n" + "="*50)
        logger.info("📊 测试结果汇总")
        logger.info("="*50)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name}: {status}")
            if result:
                passed += 1
        
        logger.info(f"\n总计: {passed}/{total} 个测试通过")
        
        if passed == total:
            logger.info("🎉 所有测试通过！OmniSharp修复成功！")
            return True
        else:
            logger.warning(f"⚠️ {total - passed} 个测试失败，需要进一步调试")
            return False


async def main():
    """主函数"""
    tester = OmniSharpTester()
    success = await tester.run_all_tests()
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试运行异常: {e}", exc_info=True)
        sys.exit(1)
