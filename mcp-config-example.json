{"_comment": "C# Assistant MCP Server Configuration Example", "_description": "这是C# Assistant MCP服务器的配置示例文件", "_usage": ["1. 复制此文件到你的MCP客户端配置目录", "2. 根据你的实际环境修改路径和参数", "3. 确保Python环境和依赖已正确安装", "4. 确保OmniSharp和ArangoDB已正确配置"], "mcpServers": {"csharp-assistant": {"command": "python", "args": ["src/csharp_assistant/mcp_server.py"], "cwd": "d:/Project/Super-Project-Assistant", "_comment_env": "环境变量配置 - 可选，如果不设置将使用.env文件中的配置", "env": {"_comment_project": "项目配置", "PROJECT_NAME": "Game-6", "PROJECT_PATH": "E:/Github/4.2.1/Game-6", "LOG_LEVEL": "INFO", "_comment_language_server": "语言服务器配置", "LANGUAGE_SERVER_ENABLED": "true", "LANGUAGE_SERVER_HOST": "127.0.0.1", "LANGUAGE_SERVER_PORT": "2087", "LANGUAGE_SERVER_TYPE": "omnisharp", "LANGUAGE_SERVER_PATH": "D:/omnisharp/OmniSharp.exe", "_comment_ai": "AI服务配置", "LLM_PROVIDER": "ollama", "AI_PROVIDER": "ollama", "LLM_MODEL": "qwen2.5:3b", "LLM_TEMPERATURE": "0.7", "LLM_MAX_TOKENS": "2000", "LLM_STREAM": "true", "_comment_local_model": "本地模型配置（用于Ollama）", "LOCAL_MODEL_NAME": "qwen2.5:3b", "LOCAL_API_BASE": "http://localhost:11434/v1", "LOCAL_CONTEXT_WINDOW": "8192", "_comment_database": "ArangoDB数据库配置", "ARANGODB_HOST": "localhost", "ARANGODB_PORT": "8529", "ARANGODB_USER": "root", "ARANGODB_PASSWORD": "12345678", "ARANGODB_DATABASE": "_system", "_comment_mcp": "MCP服务配置", "MCP_HOST": "0.0.0.0", "MCP_PORT": "8000", "_comment_debug": "调试配置", "DEBUG": "true"}}}, "_configuration_notes": {"paths": {"PROJECT_PATH": "必须指向C#项目的根目录，不是.sln文件", "LANGUAGE_SERVER_PATH": "OmniSharp.exe的完整路径", "cwd": "MCP服务器脚本所在的工作目录"}, "ai_providers": {"supported": ["openai", "gemini", "ollama"], "note": "确保AI_PROVIDER和LLM_PROVIDER使用相同的值"}, "database": {"note": "ArangoDB必须先启动并可访问", "default_database": "_system或创建专用数据库"}, "language_server": {"note": "OmniSharp需要单独下载和安装", "download": "https://github.com/OmniSharp/omnisharp-roslyn/releases"}}}