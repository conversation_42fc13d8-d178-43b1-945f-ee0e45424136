{"mcpServers": {"csharp-assistant": {"command": "python", "args": ["src/csharp_assistant/mcp_server.py"], "cwd": "d:/Project/Super-Project-Assistant", "env": {"PROJECT_NAME": "Game-6", "PROJECT_PATH": "E:/Github/4.2.1/Game-6", "LOG_LEVEL": "INFO", "LANGUAGE_SERVER_ENABLED": "true", "LANGUAGE_SERVER_HOST": "127.0.0.1", "LANGUAGE_SERVER_PORT": "2087", "LANGUAGE_SERVER_TYPE": "omnisharp", "LANGUAGE_SERVER_PATH": "D:/omnisharp/OmniSharp.exe", "LLM_PROVIDER": "ollama", "AI_PROVIDER": "ollama", "LLM_MODEL": "qwen2.5:3b", "LLM_TEMPERATURE": "0.7", "LLM_MAX_TOKENS": "2000", "LLM_STREAM": "true", "LOCAL_MODEL_NAME": "qwen2.5:3b", "LOCAL_API_BASE": "http://localhost:11434/v1", "LOCAL_CONTEXT_WINDOW": "8192", "OLLAMA_BASE_URL": "http://localhost:11434", "OLLAMA_MODEL": "qwen2.5:3b", "ARANGODB_HOST": "localhost", "ARANGODB_PORT": "8529", "ARANGODB_USER": "root", "ARANGODB_PASSWORD": "12345678", "ARANGODB_DATABASE": "_system", "MCP_HOST": "0.0.0.0", "MCP_PORT": "8000", "DEBUG": "true"}}}}