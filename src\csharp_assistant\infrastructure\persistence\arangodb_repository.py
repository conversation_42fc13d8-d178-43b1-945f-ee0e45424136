"""
知识图谱管理器
"""

import logging
from typing import Any, Dict, List, Optional
from arango import ArangoClient, ArangoError
from ...application.ports.repository_port import KnowledgeGraphPort
from ...config.config import get_config

logger = logging.getLogger(__name__)


class KnowledgeGraphManager(KnowledgeGraphPort):
    """知识图谱管理器"""

    def __init__(self):
        config = get_config()
        self.config = config
        try:
            self.client = ArangoClient(hosts=f"http://{config.arangodb_host}:{config.arangodb_port}")
            self.db = self.client.db(
                config.arangodb_database,
                username=config.arangodb_user,
                password=config.arangodb_password,
            )
            self.graph = None  # 将在initialize_schema中创建
            logger.info(f"连接到ArangoDB: {config.arangodb_host}:{config.arangodb_port}")
        except Exception as e:
            logger.error(f"连接ArangoDB失败: {e}")
            raise

    async def initialize_schema(self):
        """初始化知识图谱模式"""
        try:
            logger.info("正在初始化知识图谱模式...")

            # Create vertex collections
            vertex_collections = ["Project", "File", "Class", "Method", "Parameter", "Field"]
            for collection_name in vertex_collections:
                if not self.db.has_collection(collection_name):
                    self.db.create_collection(collection_name)
                    logger.debug(f"创建顶点集合: {collection_name}")

            # Create edge collections and define graph
            edge_definitions = [
                {"edge_collection": "CONTAINS_FILE", "from_vertex_collections": ["Project"], "to_vertex_collections": ["File"]},
                {"edge_collection": "CONTAINS_CLASS", "from_vertex_collections": ["File"], "to_vertex_collections": ["Class"]},
                {"edge_collection": "CONTAINS_METHOD", "from_vertex_collections": ["Class"], "to_vertex_collections": ["Method"]},
                {"edge_collection": "HAS_PARAMETER", "from_vertex_collections": ["Method"], "to_vertex_collections": ["Parameter"]},
                {"edge_collection": "CALLS", "from_vertex_collections": ["Method"], "to_vertex_collections": ["Method"]},
                {"edge_collection": "HAS_FIELD", "from_vertex_collections": ["Class"], "to_vertex_collections": ["Field"]},
                {"edge_collection": "INHERITS", "from_vertex_collections": ["Class"], "to_vertex_collections": ["Class"]},
                {"edge_collection": "IMPLEMENTS", "from_vertex_collections": ["Class"], "to_vertex_collections": ["Class"]},
            ]

            if not self.db.has_graph("csharp_knowledge_graph"):
                self.graph = self.db.create_graph("csharp_knowledge_graph", edge_definitions=edge_definitions)
                logger.info("创建知识图谱")
            else:
                self.graph = self.db.graph("csharp_knowledge_graph")
                # Update edge definitions if graph already exists
                for edge_def in edge_definitions:
                    try:
                        if not self.graph.has_edge_definition(edge_def["edge_collection"]):
                            self.graph.create_edge_definition(**edge_def)
                            logger.debug(f"添加边定义: {edge_def['edge_collection']}")
                    except ArangoError as e:
                        logger.warning(f"添加边定义失败 {edge_def['edge_collection']}: {e}")

        except Exception as e:
            logger.error(f"初始化知识图谱模式失败: {e}")
            raise

            # Create indexes for performance
            self._create_indexes()
            logger.info("知识图谱模式初始化完成")

        except Exception as e:
            logger.error(f"初始化知识图谱模式失败: {e}")
            raise

    def _create_indexes(self):
        """创建索引以提高查询性能"""
        try:
            # 为常用查询字段创建索引
            index_configs = [
                ("Project", ["name"]),
                ("File", ["path"]),
                ("Class", ["name", "full_name", "namespace"]),
                ("Method", ["name", "full_name"]),
                ("Field", ["name", "full_name"]),
                ("Parameter", ["name"])
            ]

            for col_name, fields in index_configs:
                collection = self.db.collection(col_name)
                existing_indexes = collection.indexes()

                for field in fields:
                    # 检查是否已存在该字段的索引
                    field_indexed = any(
                        field in idx.get("fields", [])
                        for idx in existing_indexes
                        if idx.get("type") in ["persistent", "hash"]
                    )

                    if not field_indexed:
                        try:
                            collection.add_hash_index(fields=[field], unique=False)
                            logger.debug(f"为 {col_name}.{field} 创建索引")
                        except ArangoError as e:
                            logger.warning(f"创建索引失败 {col_name}.{field}: {e}")

        except Exception as e:
            logger.error(f"创建索引失败: {e}")


    async def update_from_analysis(
        self,
        project_name: str,
        analysis: Dict[str, Any]
    ):
        """根据代码分析结果更新知识图谱"""
        if not self.graph:
            logger.error("知识图谱未初始化")
            return

        try:
            logger.info(f"更新项目 {project_name} 的知识图谱")

            # Get collections
            projects_col = self.db.collection("Project")
            files_col = self.db.collection("File")
            classes_col = self.db.collection("Class")
            methods_col = self.db.collection("Method")
            parameters_col = self.db.collection("Parameter")
            fields_col = self.db.collection("Field")

            # Get edge collections
            contains_file_edge = self.graph.edge_collection("CONTAINS_FILE")
            contains_class_edge = self.graph.edge_collection("CONTAINS_CLASS")
            contains_method_edge = self.graph.edge_collection("CONTAINS_METHOD")
            has_parameter_edge = self.graph.edge_collection("HAS_PARAMETER")
            calls_edge = self.graph.edge_collection("CALLS")
            has_field_edge = self.graph.edge_collection("HAS_FIELD")

            # Create or update Project node
            project_key = self._generate_key(project_name)
            project_doc = projects_col.insert({
                "_key": project_key,
                "name": project_name,
                "updated_at": self._get_timestamp()
            }, overwrite=True)

            logger.debug(f"更新项目节点: {project_name}")

            # 处理文件数据
            files_processed = 0
            for file_data in analysis.get("files", []):
                try:
                    file_path = file_data.get("path")
                    if not file_path:
                        continue

                    file_key = self._generate_key(file_path)
                    file_doc = files_col.insert({
                        "_key": file_key,
                        "path": file_path,
                        "updated_at": self._get_timestamp()
                    }, overwrite=True)

                    # 创建项目到文件的关系
                    try:
                        contains_file_edge.insert({
                            "_from": project_doc["_id"],
                            "_to": file_doc["_id"]
                        }, overwrite=True)
                    except Exception:
                        pass  # 关系可能已存在

                    # 处理类数据
                    await self._process_classes(file_data, file_doc, classes_col, methods_col,
                                              parameters_col, fields_col, contains_class_edge,
                                              contains_method_edge, has_parameter_edge,
                                              has_field_edge, calls_edge)

                    files_processed += 1

                except Exception as e:
                    logger.error(f"处理文件失败 {file_path}: {e}")
                    continue

            logger.info(f"成功处理 {files_processed} 个文件")

        except Exception as e:
            logger.error(f"更新知识图谱失败: {e}")
            raise

    def _generate_key(self, name: str) -> str:
        """生成安全的文档键"""
        import re
        # 移除或替换不安全的字符
        safe_key = re.sub(r'[^a-zA-Z0-9_-]', '_', name)
        # 确保键不为空且不以数字开头
        if not safe_key or safe_key[0].isdigit():
            safe_key = f"key_{safe_key}"
        return safe_key[:254]  # ArangoDB键长度限制

    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.utcnow().isoformat()

    async def _process_classes(self, file_data: Dict[str, Any], file_doc: Dict[str, Any],
                             classes_col, methods_col, parameters_col, fields_col,
                             contains_class_edge, contains_method_edge,
                             has_parameter_edge, has_field_edge, calls_edge):
        """处理类数据"""
        for class_data in file_data.get("classes", []):
            try:
                class_name = class_data.get("name")
                class_namespace = class_data.get("namespace", "")
                if not class_name:
                    continue

                class_full_name = f"{class_namespace}.{class_name}" if class_namespace else class_name
                class_key = self._generate_key(class_full_name)

                class_doc = classes_col.insert({
                    "_key": class_key,
                    "name": class_name,
                    "namespace": class_namespace,
                    "full_name": class_full_name,
                    "access_modifier": class_data.get("access_modifier", "public"),
                    "is_static": class_data.get("is_static", False),
                    "is_abstract": class_data.get("is_abstract", False),
                    "is_sealed": class_data.get("is_sealed", False),
                    "updated_at": self._get_timestamp()
                }, overwrite=True)

                # 创建文件到类的关系
                try:
                    contains_class_edge.insert({
                        "_from": file_doc["_id"],
                        "_to": class_doc["_id"]
                    }, overwrite=True)
                except ArangoError:
                    pass  # 关系可能已存在

                # 处理方法
                await self._process_methods(class_data, class_doc, class_full_name,
                                          methods_col, parameters_col, contains_method_edge,
                                          has_parameter_edge, calls_edge)

                # 处理字段
                await self._process_fields(class_data, class_doc, class_full_name,
                                         fields_col, has_field_edge)

            except Exception as e:
                logger.error(f"处理类失败 {class_name}: {e}")
                continue

    async def _process_methods(self, class_data: Dict[str, Any], class_doc: Dict[str, Any],
                             class_full_name: str, methods_col, parameters_col,
                             contains_method_edge, has_parameter_edge, calls_edge):
        """处理方法数据"""
        for method_data in class_data.get("methods", []):
            try:
                method_name = method_data.get("name")
                if not method_name:
                    continue

                method_full_name = f"{class_full_name}.{method_name}"
                method_key = self._generate_key(method_full_name)

                method_doc = methods_col.insert({
                    "_key": method_key,
                    "name": method_name,
                    "full_name": method_full_name,
                    "return_type": method_data.get("return_type", "void"),
                    "access_modifier": method_data.get("access_modifier", "public"),
                    "is_static": method_data.get("is_static", False),
                    "is_async": method_data.get("is_async", False),
                    "is_virtual": method_data.get("is_virtual", False),
                    "is_override": method_data.get("is_override", False),
                    "is_abstract": method_data.get("is_abstract", False),
                    "updated_at": self._get_timestamp()
                }, overwrite=True)

                # 创建类到方法的关系
                try:
                    contains_method_edge.insert({
                        "_from": class_doc["_id"],
                        "_to": method_doc["_id"]
                    }, overwrite=True)
                except ArangoError:
                    pass

                # 处理参数
                for param_data in method_data.get("parameters", []):
                    try:
                        param_name = param_data.get("name")
                        if not param_name:
                            continue

                        param_key = self._generate_key(f"{method_full_name}.{param_name}")
                        param_doc = parameters_col.insert({
                            "_key": param_key,
                            "name": param_name,
                            "type": param_data.get("type", "object"),
                            "updated_at": self._get_timestamp()
                        }, overwrite=True)

                        # 创建方法到参数的关系
                        try:
                            has_parameter_edge.insert({
                                "_from": method_doc["_id"],
                                "_to": param_doc["_id"]
                            }, overwrite=True)
                        except ArangoError:
                            pass

                    except Exception as e:
                        logger.error(f"处理参数失败 {param_name}: {e}")
                        continue

                # 处理方法调用关系
                for called_method_name in method_data.get("calls", []):
                    try:
                        if called_method_name and called_method_name != method_full_name:
                            callee_key = self._generate_key(called_method_name)
                            # 创建或获取被调用方法
                            callee_doc = methods_col.insert({
                                "_key": callee_key,
                                "full_name": called_method_name,
                                "updated_at": self._get_timestamp()
                            }, overwrite=True)

                            # 创建调用关系
                            try:
                                calls_edge.insert({
                                    "_from": method_doc["_id"],
                                    "_to": callee_doc["_id"]
                                }, overwrite=True)
                            except ArangoError:
                                pass

                    except Exception as e:
                        logger.error(f"处理方法调用失败 {called_method_name}: {e}")
                        continue

            except Exception as e:
                logger.error(f"处理方法失败 {method_name}: {e}")
                continue

    async def _process_fields(self, class_data: Dict[str, Any], class_doc: Dict[str, Any],
                            class_full_name: str, fields_col, has_field_edge):
        """处理字段数据"""
        for field_data in class_data.get("fields", []):
            try:
                field_name = field_data.get("name")
                if not field_name:
                    continue

                field_full_name = f"{class_full_name}.{field_name}"
                field_key = self._generate_key(field_full_name)

                field_doc = fields_col.insert({
                    "_key": field_key,
                    "name": field_name,
                    "full_name": field_full_name,
                    "type": field_data.get("type", "object"),
                    "access_modifier": field_data.get("access_modifier", "private"),
                    "is_static": field_data.get("is_static", False),
                    "has_getter": field_data.get("has_getter", False),
                    "has_setter": field_data.get("has_setter", False),
                    "is_auto_property": field_data.get("is_auto_property", False),
                    "updated_at": self._get_timestamp()
                }, overwrite=True)

                # 创建类到字段的关系
                try:
                    has_field_edge.insert({
                        "_from": class_doc["_id"],
                        "_to": field_doc["_id"]
                    }, overwrite=True)
                except ArangoError:
                    pass

            except Exception as e:
                logger.error(f"处理字段失败 {field_name}: {e}")
                continue



    async def query_context(
        self,
        query: str,
        limit: int = 10
    ) -> List[Dict]:
        """查询相关知识图谱上下文"""
        if not self.graph:
            logger.error("知识图谱未初始化")
            return []

        try:
            logger.debug(f"查询知识图谱: {query}")

            # 使用AQL进行多集合搜索
            aql_query = """
            LET classes = (
                FOR c IN Class
                    FILTER LIKE(LOWER(c.name), LOWER(@query_param), true)
                        OR LIKE(LOWER(c.full_name), LOWER(@query_param), true)
                        OR LIKE(LOWER(c.namespace), LOWER(@query_param), true)
                    LIMIT @limit_param
                    RETURN MERGE(c, {type: "class"})
            )
            LET methods = (
                FOR m IN Method
                    FILTER LIKE(LOWER(m.name), LOWER(@query_param), true)
                        OR LIKE(LOWER(m.full_name), LOWER(@query_param), true)
                        OR LIKE(LOWER(m.return_type), LOWER(@query_param), true)
                    LIMIT @limit_param
                    RETURN MERGE(m, {type: "method"})
            )
            LET fields = (
                FOR f IN Field
                    FILTER LIKE(LOWER(f.name), LOWER(@query_param), true)
                        OR LIKE(LOWER(f.full_name), LOWER(@query_param), true)
                        OR LIKE(LOWER(f.type), LOWER(@query_param), true)
                    LIMIT @limit_param
                    RETURN MERGE(f, {type: "field"})
            )
            FOR result IN UNION(classes, methods, fields)
                SORT result.name
                LIMIT @total_limit
                RETURN result
            """

            cursor = self.db.aql.execute(
                aql_query,
                bind_vars={
                    "query_param": f"%{query}%",
                    "limit_param": limit // 3,  # 每个类型的限制
                    "total_limit": limit
                }
            )

            results = [doc for doc in cursor]
            logger.debug(f"查询返回 {len(results)} 个结果")
            return results

        except Exception as e:
            logger.error(f"查询知识图谱失败: {e}")
            return []

    async def get_class_details(self, class_name: str) -> Optional[Dict[str, Any]]:
        """获取类的详细信息，包括方法和字段"""
        if not self.graph:
            return None

        try:
            aql_query = """
            FOR c IN Class
                FILTER c.name == @class_name OR c.full_name == @class_name
                LET methods = (
                    FOR v, e IN 1..1 OUTBOUND c CONTAINS_METHOD
                        RETURN v
                )
                LET fields = (
                    FOR v, e IN 1..1 OUTBOUND c HAS_FIELD
                        RETURN v
                )
                RETURN MERGE(c, {
                    methods: methods,
                    fields: fields
                })
            """

            cursor = self.db.aql.execute(
                aql_query,
                bind_vars={"class_name": class_name}
            )

            results = [doc for doc in cursor]
            return results[0] if results else None

        except Exception as e:
            logger.error(f"获取类详情失败: {e}")
            return None

    async def get_method_calls(self, method_name: str) -> List[Dict[str, Any]]:
        """获取方法的调用关系"""
        if not self.graph:
            return []

        try:
            aql_query = """
            FOR m IN Method
                FILTER m.name == @method_name OR m.full_name == @method_name
                LET calls = (
                    FOR v, e IN 1..1 OUTBOUND m CALLS
                        RETURN v
                )
                LET called_by = (
                    FOR v, e IN 1..1 INBOUND m CALLS
                        RETURN v
                )
                RETURN {
                    method: m,
                    calls: calls,
                    called_by: called_by
                }
            """

            cursor = self.db.aql.execute(
                aql_query,
                bind_vars={"method_name": method_name}
            )

            return [doc for doc in cursor]

        except Exception as e:
            logger.error(f"获取方法调用关系失败: {e}")
            return []

    def close(self):
        """关闭数据库连接"""
        # ArangoDB client doesn't require explicit close for HTTP connections
        pass